<template>
  <el-dialog title="全链路" v-model="allChainVisible" fullscreen class="position-dialog">
    <template #header> <el-button type="primary" @click="closeTraceLogs" :icon="Back">返回</el-button><Strong></Strong> </template>
    <ExperiencePosition ref="markPositionRef"></ExperiencePosition>
  </el-dialog>
  <div class="process-box" :class="{ selected: isSelected }" @click="$emit('select')">
    <div class="label" style="font-size: 16px; font-weight: bold">流程{{ label }}:</div>
    <el-tooltip :content="processName" placement="top" :show-after="500" :hide-after="0">
      <div class="process-name">{{ processName }}</div>
    </el-tooltip>

    <div class="flexBetween" style="margin-top: 8px">
      <router-link v-if="processName" :to="allChainTo" style="margin-left: 1px" @click.stop>
        <el-button size="small" round @click.stop="events.allChain" :disabled="dataC.isEmpty(markRecordId)">查看全链路</el-button>
      </router-link>
      <el-button v-if="processName" class="edit-params-btn" size="small" round :type="isEditInputArgs ? 'primary' : 'pl'" @click.stop="$emit('editParams')">
        修改参数
      </el-button>
    </div>
    <el-button v-show="hasValue" class="clear-btn" type="danger" circle size="small" @click.stop="$emit('clear')">
      <el-icon><Close /></el-icon>
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { Close } from "@element-plus/icons-vue";
import ExperiencePosition from "./ExperiencePosition.vue";
import useCtx from "@/hooks/useCtx";
import { keys} from "lodash";
import { dataC } from "turing-plugin";
import { ref, reactive, nextTick, computed } from "vue";
import { fa } from "element-plus/es/locale";
const { $app, $router, proxy } = useCtx();

const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  markRecordId:{
    type: String,
    default: true,
  },
  processName: {
    type: String,
    default: "",
  },
  isSelected: {
    type: Boolean,
    default: false,
  },
  isEditInputArgs: {
    type: Boolean,
    default: false,
  },
  hasValue: {
    type: Boolean,
    default: false,
  },
});

defineEmits(["select", "clear", "editParams"]);

//全链路显示
const allChainVisible = ref(false);

function closeTraceLogs(){
  allChainVisible.value = false;
  $router.push({ name: `experience-index`});
}


const getAllChainToQuery = () => {
  const oldQuery = $router.currentRoute.value.query;
  return {...oldQuery, markRecordId: props.markRecordId, tag: props.label}
};

//获取全链路跳转url地址
const allChainTo = computed(() => {
  const query = getAllChainToQuery();
  const routerQuery = new URLSearchParams();
  keys(query).forEach((key) => {
    routerQuery.append(key, String(query[key]));
  });
  return `/experience-index/position?${routerQuery.toString()}`;
});


const events = reactive({
  allChain: async () => {
    //阻止原生路由跳转事件
    event.preventDefault();
    //修改当前路由
    await $router.push({ name: `experience-index`, query: getAllChainToQuery() });
    //打开一个全屏dialog
    allChainVisible.value = true;
    //刷新全链路
    nextTick(() => {
      console.log("mark-strategy nextTick");
      proxy.$refs.markPositionRef.getTraceinfo();
    });
  },
});
</script>

<style lang="scss" scoped>
.process-box {
  height: 120px;
  width: 200px;
  padding: 5px;
  background: #f6f3f3;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  .label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }

  .process-name {
    font-size: 14px;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    padding-right: 24px;
    margin-bottom: 8px;
  }

  .edit-params-btn {
    width: 80px;
    margin: 0 auto;
    font-size: 12px;
    padding: 4px 12px;
    transition: all 0.3s ease;
  }

  .clear-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);

    .clear-btn {
      opacity: 1;
    }
  }

  &.selected {
    background: #ffffff;
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.043);
    transform: translateY(-2px);

    &::before {
      transform: scaleX(1);
    }

    .process-name {
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--el-color-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }
}
</style>
