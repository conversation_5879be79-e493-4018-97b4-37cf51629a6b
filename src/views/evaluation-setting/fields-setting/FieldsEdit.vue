<template>
  <my-drawer class="fields-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <my-form labelWidth="130px" ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit">
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { assign, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import useCtx from "@/hooks/useCtx";
import * as evalSettingApi from "@/api/eval-setting";

const { $app } = useCtx();

const props = defineProps({
  categoryEnum: { type: Array, default: () => [] },
});

const emit = defineEmits(["save-data"]);

// 弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新增字段配置";
  if (formType.value == "edit") return "编辑字段配置";
});
const dialogVisible = ref<boolean>(false);

const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};

const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

// 是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  field: "",
  name: "",
  category: "",
  type: "string",
  defaultValue: "",
  description: "",
  sample: "",
  path: "",
  showInTrace: true,
};

let ruleForm = ref<any>(assign({}, defaultForm));

// 表单验证规则
const rules = reactive<FormRules>({
  field: [
    { required: true, message: "字段名称不能为空", trigger: "blur" },
    { min: 1, max: 50, message: "字段名称长度在 1 到 50 个字符", trigger: "blur" },
    {
      pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
      message: "字段名称必须以字母开头，只能包含字母、数字和下划线",
      trigger: "blur",
    },
  ],
  name: [
    { required: true, message: "字段标签不能为空", trigger: "blur" },
    { min: 1, max: 100, message: "字段标签长度在 1 到 100 个字符", trigger: "blur" },
  ],
  category: [
    { required: true, message: "字段分类不能为空", trigger: "change" },
  ],
  type: [
    { required: true, message: "字段类型不能为空", trigger: "change" },
  ],
});

// 表单项
const formItems = ref<any>({
  field: {
    label: "字段",
    type: "input",
    attrs: {
      maxlength: 50,
      placeholder: "请输入字段",
    },
    disabled: () => isUpdate.value,
  },
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 100,
      placeholder: "请输入字段名称",
    },
  },
  category: {
    label: "字段分类",
    type: "select",
    options: computed(() => props.categoryEnum),
    attrs: {
      allowCreate: true,
      placeholder: "请选择或输入字段分类",
    },
  },
  type: {
    label: "字段类型",
    type: "select",
    options: [
      { value: "string", label: "string" },
      { value: "number", label: "number" },
      { value: "boolean", label: "boolean" },
      { value: "date", label: "date" },
      { value: "datetime", label: "datetime" },
      { value: "text", label: "text" },
    ],
    attrs: {
      placeholder: "请选择字段类型",
    },
  },
    description: {
    label: "描述",
    type: "textarea",
    attrs: {
      maxlength: 500,
      placeholder: "请输入字段描述",
      rows: 3,
    },
  },
  defaultValue: {
    label: "默认值",
    type: "input",
    attrs: {
      placeholder: "请输入默认值",
    },
  },
  showInTrace: {
    label: "全链路中是否显示",
    type: "switch",
  },
  sample: {
    label: "示例",
    type: "input",
    attrs: {
      placeholder: "请输入样例",
    },
  },
  path: {
    label: "路径",
    type: "input",
    attrs: {
      placeholder: "请输入路径",
    },
  }
});

// 提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);

  form.idx = 0;

  if (isUpdate.value) {
    evalSettingApi.save(form).then(() => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    evalSettingApi.save(form).then(() => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};

// 打开窗口
const openWindow = (type: string, item: any) => {
  formType.value = type;
  if (type === "edit") {
    ruleForm.value = assign({}, defaultForm, item);
  } else {
    ruleForm.value = assign({}, defaultForm);
  }
  dialogVisible.value = true;
};

// 暴露方法
defineExpose({
  openWindow,
});
</script>

<style lang="scss" scoped>
</style>
