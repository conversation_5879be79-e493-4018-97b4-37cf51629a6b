<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="evaluation-task-index">
      <!-- 左右布局容器 -->
      <div class="layout-container">
        <!-- 左侧树结构 -->
        <div class="left-panel">
          <module-tree
            :module="module"
            @tree-node-click="events.handleTreeNodeClick"
          />
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-panel">
          <analysis-edit
            ref="analysisPlanEditRef"
            @save-data="events.loadInstTableList"
            :treeNode="currentNode"
            :missiontypeEnum="missiontypeEnum"
            :publishList="publishList"
            :standardList="standardList"
          ></analysis-edit>
          <analysis-table
            ref="analysisPlanTableRef"
            @edit-data-inst="events.openInstEditWindow"
            @statistic-data="events.openStatisticPage"
            :treeNode="currentNode"
            :missiontypeEnum="missiontypeEnum"
            :standardList="standardList"
          ></analysis-table>
        </div>
      </div>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import analysisTable from "./table.vue";
import analysisEdit from "./edit.vue";
import ModuleTree from "@/views/standard/module/ModuleTree.vue";
import * as standardApi from "@/api/eval-mark-standard";
import useCtx from "@/hooks/useCtx";
import * as evaluationApi from "@/api/eval-task";
import * as querySetApi from "@/api/eval-query-set";
import * as markApi from "@/api/eval-mark";
import useStore from "@/store";
import { storeToRefs } from "pinia";
const { common } = useStore();
const { $app, proxy, $router } = useCtx();
const routeName = "evaluation-task";
let publishList = ref([]);
let settingEnum = ref<any[]>([]);
const standardList = ref<any[]>([]);
const module = ref("CPRW");
const currentNode = ref("ROOT");
const missiontypeEnum = [
  { value: 1, label: "在线测评", type: "success" },
  { value: 2, label: "离线测评", type: "warning" },
];
const events = reactive({
  loadInstTableList: () => {
    proxy.$refs["analysisPlanTableRef"].loadList();
  },
  openInstEditWindow: (type: string, item: any) => {
    proxy.$refs["analysisPlanEditRef"].openWindow(type, item);
  },
  openStatisticPage: (item: any) => {
    $router.push({
      name: `${routeName}::details`,
      query: {
        missionId: item.id,
        metaLabel: [item.name],
        name: item.name,
        description: item.description,
        label: item.label,
        size: item.size,
        typeRender: item.typeRender,
        timeRender: item.timeRender,
        markCategoryIdRender: item.markCategoryIdRender,
        userCount: item.userCount,
        queryCount: item.queryCount,
      },
    });
  },
  handleTreeNodeClick:() => {
    currentNode.value = $router.currentRoute.value.query.catalog as string || ""; // 获取当前选中树结点
    proxy.$refs.analysisPlanTableRef.loadList(currentNode.value);
  }
});
const getPublishList = () => {
  querySetApi.publishList().then((res) => {
    publishList.value = res.data.map((item) => ({
      value: item.id,
      label: item.name,
    }));
  });
};

const getStandardList = async () => {
  const res = await standardApi.getList();
  console.log(res);
  standardList.value = res.data;
  standardList.value.map((item) => {
    item.value = item.id;
    item.label = item.name;
  });
};

getStandardList();

getPublishList();
</script>
<style lang="scss">
.evaluation-task-index {
  .layout-container {
    display: flex;
    height: 100%;
  }

  .left-panel {
    width: 300px;
    min-width: 200px;
    background: #fff;
    border-right: 1px solid #f0f0f0;
    padding: 16px 0;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .right-panel {
    flex: 1;
    padding: 24px;
    background: #fafbfc;
    overflow-y: auto;
  }

}

</style>
