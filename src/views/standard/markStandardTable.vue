<template>
  <div class="meta-dict-table">
    <el-card class="info-card">
      <table-page ref="myWordTableRef" :columns="columns" :query="query" :loadDataApi="loadListData"
        :transformListData="transformListData" :loadImmediately="false" :operationAuth="operationAuth"
        :operations="operations" @operation="handleOperation">
        <template #query>
          <div class="flexBetweenStart">
            <my-query :queryItems="queryItems" :refresh-btn="{ show: true }" @search="events.search" @reset="events.reset"
              supportFold @toggle="events.toggleQuery" />
            <my-operation>
              <template #buttonGroup>
                <my-button type="primary" @click="handleAdd">新建测评标准</my-button>

              </template>
            </my-operation>
          </div>
        </template>
        <template #header>
          <div class="header">
            <span>{{ treeNode?.name }}</span>
          </div>
        </template>
      </table-page>

    </el-card>
  </div>
</template>
  
<script lang="ts" setup>
import { ref, reactive, nextTick, watch } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as markStandardApi from "@/api/eval-mark-standard";
import useStore from "@/store";
import { assign, pick, keys } from "lodash";
import * as util from "@/utils/common";
import * as commonApi from "@/api/common";
import { da } from "element-plus/es/locale";
import siteVersionEdit from "./standardGroupDetail.vue";
const { $app, proxy, $router } = useCtx();
const { common } = useStore();
const props = defineProps({
  treeNode: { type: String, default: '' },
  treeData: { type: Array },
  operationAuth: { type: String },
});

const routeName = "mark-standard";



//列配置
const columns = ref([

  {
    prop: "name",
    label: "名称",
    minWidth: 200,
    custom: "link",
    customRender: {
      click: (record: any) => {
        events.detail(record);
      },
    },
    fixed: 'left'
  },

  {
    prop: "enabled",
    label: "是否启用",
    width: 112,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        return new Promise((resolve) => {
          $app
            .$confirm({
              title: `确定${record.enabled ? "停用" : "启用"}“${record.name}”吗？`,
            })
            .then(() => {
              markStandardApi.enableOrDisable(record.id, !record.enabled)
                .then((res) => {
                  loadList();
                  $app.$message.success(record.enabled ? "停用成功" : "启用成功");
                  resolve(true); // 操作成功，允许切换
                })
                .catch(() => {
                  resolve(false); // 操作失败，不允许切换
                });
            })
            .catch(() => {
              resolve(false); // 用户取消，不允许切换
            });
        });
      },
    },
  },
  // 文本可复制
  {
    prop: "description",
    label: "描述",
    minWidth: 120,
  },

  {
    prop: "recallDimsGroupName",
    label: "维度标准",
    minWidth: 120,
    sortable: false
  },
  {
    prop: "goodRender",
    label: "goodUrl定义",
    minWidth: 160,
    sortable: false
  },
  {
    prop: "badRender",
    label: "badUrl定义",
    minWidth: 160,
    sortable: false
  },
  {
    prop: "ignoreQueryDimsGroupName",
    label: "query类弃标标准",
    minWidth: 200,
    sortable: false
  },
  {
    prop: "ignoreDocDimsGroupName",
    label: "doc类弃标标准",
    minWidth: 150,
    sortable: false
    
  },
  {
    prop: "chatDimsGroupName",
    label: "chat标注标准",
    minWidth: 150,
    sortable: false
  },
  {
    prop: "catalogName",
    label: "所属目录",
    minWidth: 120,
    sortable: false
  },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" },
]);
//查询面板
const query = ref<any>({});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    width: "220px",
    modelValue: "",
    attrs: {
      placeholder: "名称",
    },
  }
});

// 格式化 classifyRules
function formatClassifyRules(targetGroup: any) {


  if (!targetGroup || !targetGroup.ruleGroup || targetGroup.ruleGroup.length === 0) return '';

  // 格式化每个 ruleGroupItem
  return targetGroup.ruleGroup.map(ruleGroupItem => {
    return ruleGroupItem.dimOptions.map(dimOption => {
      return `${dimOption.dimName}:${dimOption.optionNames.join(',')}`;
    }).join(',');
  }).join('、');
};




const handleAdd = () => {
  emit('add-data', { mode: 'add', data: {} });
};

const operations = [
  {
    type: "edit",
    label: "编辑",

  },
  {
    type: "copy",
    label: "复制",

  },
  { type: "delete", label: "删除", btnType: "danger" },
];

const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};



//列表查询
const loadListData = (data: any) => {

  return new Promise((resolve: any) => {
    if (!props.treeNode) {
      return
    }
    let params: any = {
      ...data,
      code: props.treeNode,
      search: query.value.search
    };

    markStandardApi.getTablePage(params).then((result) => {

      result.content = result.content.map((item) => ({
        ...item,
      }));
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    // 确保 classifyRules 存在

    x.badRender = formatClassifyRules(x.classifyRules.find(item => item.name === 'bad'));
    x.goodRender = formatClassifyRules(x.classifyRules.find(item => item.name === 'good'));


    return x;
  });
};

//事件列表
const events = reactive({
  toggleQuery: () => {
    nextTick(() => {
      // proxy.$refs.myWordTableRef?.mediaHeight();
      window.dispatchEvent(new Event("resize"));
    });
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {
    for (let key in obj) {
      queryItems.value[key].modelValue = obj[key].modelValue;
    }
  },
  add: () => {
    emit('edit-data', { mode: 'add', data: {} });
  },


  edit: (record: any) => {
    emit('edit-data', { mode: 'edit', data: record });
  },



  // 复制
  copy: async (record: any) => {
    try {
      await markStandardApi.copy(record.id);
      $app.$message.success("复制成功");
      loadList();
    } catch (error) {
      console.error("复制失败:", error);
      $app.$message.error("复制失败");
    }
  },

  // 删除
  delete: async (record: any) => {
    try {
      await markStandardApi.deleteRecord(record.id)
      $app.$message.success("删除成功");
      loadList();
    } catch (error) {
      console.error("删除失败:", error);
      $app.$message.error("删除失败");
    }
  },


  // 点击标准名称查看维度列表明细
  detail: (record: any) => {
    console.log("详情", record)
    $router.push({
      name: `${routeName}::details`,
      query: { metaLabel: record.name, recordId: record.id, goodUrl: record.goodRender, badUrl: record.badRender }
    });
  }


});
const loadList = () => {
  proxy.$refs.myWordTableRef?.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "updateTree", "add-data"]);
//接口暴露
defineExpose({
  loadList

});
// watch监听
watch(
  () => props.treeNode,
  (val: any) => {
    if (!val) {
      return;
    }
    loadList();

  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.meta-dict-table {
  height: 100%;

  .header {
    span {
      margin-right: 20px;
    }

    span:first-child {
      font-weight: 550;
    }

    ::v-deep {
      .el-switch {
        height: 0px;
        margin-left: 10px;
      }
    }
  }
}

::v-deep {
  .t-query {
    width: 80%;
    flex: 1;
  }
}

.info-card {
  height: 100%;
  width: 100%;

  ::v-deep {
    .el-card__body {
      padding: 0;
      height: 100%;
    }
  }
}
</style>