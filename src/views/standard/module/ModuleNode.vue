<template>
  <my-dialog class="module-node" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" label-width="60" style="margin-top: 10px" />
  </my-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import useCtx from "@/hooks/useCtx";
import useValidate from "@/hooks/validate";

const { $app } = useCtx();
const { validateNameRule } = useValidate();

// 类型定义
type FormData = {
  parentCode: string;
  code: string;
  name: string;
  description: string;
};

// 弹窗相关
const dialogVisible = ref(false);
const formType = ref<"add" | "edit">("add");
const dialogTitle = computed(() => `${formType.value === "add" ? "新建" : "编辑"}目录`);

// 表单相关
const formRef = ref();
const defaultForm: FormData = {
  parentCode: "",
  code: "",
  name: "",
  description: "",
};
const ruleForm = reactive<FormData>(cloneDeep(defaultForm));

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      trigger: "blur",
      validator: (rule, value, callback) => validateNameRule(rule, value, callback, "请输入名称"),
    },
  ],
});

// 表单项配置
const formItems = reactive({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: {
      maxlength: 255,
      rows: 5,
    },
  },
});

// 事件处理
const handleClose = () => {
  formRef.value?.resetForm();
  Object.assign(ruleForm, cloneDeep(defaultForm));
  dialogVisible.value = false;
};

const handleConfirm = () => {
  formRef.value?.submitForm((valid: boolean) => {
    if (valid) {
      const formData = cloneDeep(ruleForm);
      emit(formType.value === "add" ? "add-data" : "edit-data", formData);
      handleClose();
    }
  });
};

// 打开窗口
const openWindow = (type: "add" | "edit", row?: Partial<FormData>) => {
  formType.value = type;
  dialogVisible.value = true;

  if (type === "edit" && row) {
    Object.assign(ruleForm, row);
  } else {
    Object.assign(ruleForm, cloneDeep(defaultForm), row || {});
  }
};

// 事件声明
const emit = defineEmits<{
  (e: "add-data", data: FormData): void;
  (e: "edit-data", data: FormData): void;
}>();

// 接口暴露
defineExpose({ openWindow });
</script>

<style lang="scss">
.module-node {
  .el-dialog__header {
    padding: 5px !important;
  }

  .el-dialog__body {
    padding: 5px !important;
  }
}
</style>
