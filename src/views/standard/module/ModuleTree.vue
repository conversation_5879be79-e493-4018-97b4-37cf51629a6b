<template>
  <div class="left-wrap">
    <ModuleNode ref="treeNodeRef" @add-data="events.addCatalog" @edit-data="events.editCatalog" />

    <div>
      <el-input v-model="filterText" placeholder="请输入名称" @keyup.enter="filterStr = filterText">
        <template #append>
          <el-button :icon="Search" @click="filterStr = filterText" size="small" />
        </template>
      </el-input>
    </div>
    <el-tree
      v-if="treeData.length > 0"
      ref="treeRef"
      node-key="code"
      :data="treeData"
      :props="{ children: 'children', label: 'name' }"
      :expand-on-click-node="false"
      highlight-current
      draggable
      @node-drop="events.handleNodeDrag"
      style="max-width: 600px"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node" @click="events.handleTreeNodeClick(data)">
          <span class="text" :title="data.description">
            <el-text>
              <el-icon class="primary-color"><Folder /></el-icon>&nbsp;{{ node.label }}
            </el-text>
          </span>

          <el-popover :ref="`menuRef-${data.code}`" placement="right-start" trigger="click" width="auto" popper-class="operate-popover">
            <template #reference>
              <el-icon class="icon-more" @click.stop=""><MoreFilled /></el-icon>
            </template>
            <template #default>
              <div class="items">
                <!-- <template v-for="kind in supportedKinds" :key="kind">
                  <my-button v-if="moduleData.supportKinds.includes(kind)" link @click="events.addBucket(kind, data)" :operation-auth="operationAuth">
                    <el-icon :class="kindIconClasses[kind]"><component :is="kindIcons[kind]" /></el-icon>
                    &nbsp;{{ kindLabels[kind] }}
                  </my-button>
                </template> -->
                <el-divider />
                <my-button link @click="events.openAddSameLevelCatalogWindow(data)" :operation-auth="operationAuth">
                  <el-icon class="success-color"><Plus /></el-icon>&nbsp;同级目录
                </my-button>
                <my-button link @click="events.openAddChildLevelCatalogWindow(data)" :operation-auth="operationAuth">
                  <el-icon class="success-color"><Plus /></el-icon>&nbsp;子级目录
                </my-button>
                <el-divider />
                <my-button link @click="events.openEditCatalogWindow(data)" :operation-auth="operationAuth">
                  <el-icon class="primary-color"><EditPen /></el-icon>&nbsp;编辑目录
                </my-button>
                <my-button link @click="events.deleteCatalog(data)" :operation-auth="operationAuth">
                  <el-icon class="danger-color"><Delete /></el-icon>&nbsp;删除目录
                </my-button>
              </div>
            </template>
          </el-popover>
        </span>
      </template>
    </el-tree>

    <el-button v-if="!filterStr && treeData.length === 0" type="primary" @click="events.openAddFirstCatalogWindow()"> 新建目录 </el-button>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { cloneDeep } from "lodash";
import { Folder, MoreFilled, Plus, Tickets, Document, Collection, Memo, EditPen, Delete, Search } from "@element-plus/icons-vue";
import ModuleNode from "./ModuleNode.vue";
import useCtx from "@/hooks/useCtx";
import * as markModuleApi from "@/api/eval-mark-module";

const { $app, proxy, $router } = useCtx();

const props = defineProps({
  operationAuth: { type: String },
  module: {type: String, default: ''}

});


// 响应式数据
const treeRef = ref();
const treeNodeRef = ref();
const filterText = ref("");
const filterStr = ref("");
const namespace = ref("");
const moduleData = ref({
  catalogTree: null,
  supportKinds: [],
  id: "",
});


const treeData = computed(() => {
  if (!moduleData.value.catalogTree) return [];

  const traverse = (node) => {
    const nodeMatches = node.name.includes(filterStr.value);
    const newChildren = [];

    node.children?.forEach((child) => {
      const result = traverse(child);
      if (result) {
        newChildren.push(result);
      }
    });

    return nodeMatches || newChildren.length > 0 ? { ...node, children: newChildren } : null;
  };

  const result = traverse(moduleData.value.catalogTree);
  return result?.children || [];
});

// 事件对象
const events = {
  getCatelogTree: async () => {
    
    moduleData.value = await markModuleApi.find(props.module);
    initCatalogTree(moduleData.value.catalogTree);

    if (treeData.value[0]?.code) {
      
      events.handleTreeNodeClick(treeData.value[0]);
    }
  },
  openAddFirstCatalogWindow: () => {
    treeNodeRef.value.openWindow("add", {
      code: generateCode(),
      parentCode: "ROOT",
    });
  },
  openAddSameLevelCatalogWindow: (catalogData) => {
    proxy.$refs[`menuRef-${catalogData.code}`].hide();
    treeNodeRef.value.openWindow("add", {
      code: generateCode(),
      parentCode: catalogData.parentCode,
    });
  },
  openAddChildLevelCatalogWindow: (catalogData) => {
    proxy.$refs[`menuRef-${catalogData.code}`].hide();
    treeNodeRef.value.openWindow("add", {
      code: generateCode(),
      parentCode: catalogData.code,
    });
  },
  openEditCatalogWindow: (catalogData) => {
    proxy.$refs[`menuRef-${catalogData.code}`].hide();
    treeNodeRef.value.openWindow("edit", catalogData);
  },
  addCatalog: async (catalogData) => {
    const newTree = cloneDeep(moduleData.value.catalogTree);
    addTreeNode(newTree, catalogData);
    const param = {moduleCode: props.module, catalogTree: newTree};
    await markModuleApi.save(param);
    moduleData.value.catalogTree = newTree;
    nextTick(() => {
      treeRef.value.setCurrentKey(catalogData.code);
      treeRef.value.setExpandedKeys([catalogData.code]);
    });
    events.handleTreeNodeClick(catalogData);
  },
  editCatalog: async (catalogData) => {
    const newTree = cloneDeep(moduleData.value.catalogTree);
    editTreeNode(newTree, catalogData);
    // todo 这里要区分moduleCode模块
    const param = {moduleCode: props.module, catalogTree: newTree};
    await markModuleApi.save(param);
    moduleData.value.catalogTree = newTree;
    events.handleTreeNodeClick(catalogData);
  },
  deleteCatalog: (catalogData) => {
    proxy.$refs[`menuRef-${catalogData.code}`].hide();
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${catalogData.name}?`,
      })
      .then(async () => {
        const newTree = cloneDeep(moduleData.value.catalogTree);
        deleteTreeNode(newTree, catalogData);
        // todo 这里要区分moduleCode模块
        await markModuleApi.deleteCatelog(catalogData.code, props.module);
        moduleData.value.catalogTree = newTree;

        if (catalogData.parentCode) {
          const parentNode = getNode(catalogData.parentCode);
          if (parentNode) {
            events.handleTreeNodeClick(parentNode.data);
          }
        }
      });
  },
  // addBucket: (kindCode, catalogData) => {
  //   proxy.$refs[`menuRef-${catalogData.code}`].hide();
  //   emits("treeMenuClick", kindCode, catalogData.code);
  // },
  handleTreeNodeClick: (catalogData) => {
    nextTick(() => {
      setCurrentKey(catalogData.code);
    });
    
    const currentRoute = $router.currentRoute.value;
    $router
      .push({
        name: currentRoute.name,
        query: { ...currentRoute.query, catalog: catalogData.code },
      })
      .then(() => {
        emits("treeNodeClick");
      });
  },
  handleNodeDrag: async (opNode, endNode, position) => {

    const catalogData = cloneDeep(opNode.data);
    const newTree = cloneDeep(moduleData.value.catalogTree);

    deleteTreeNode(newTree, catalogData);

    if (position === "inner") {
      catalogData.parentCode = endNode.data.code;
    } else {
      catalogData.parentCode = endNode.data.parentCode;
    }

    addTreeNode(newTree, catalogData);
    let param = {
      moduleCode: props.module,
      catalogTree: newTree
    }
    await markModuleApi.save(param);
    moduleData.value.catalogTree = newTree;
  },
};

// 树操作方法
const addTreeNode = (cur, catalogData) => {
  if (!cur) return;
  if (cur.code === catalogData.parentCode) {
    cur.children = cur.children || [];
    cur.children.push(catalogData);
    return;
  }
  cur.children?.forEach((child) => addTreeNode(child, catalogData));
};

const editTreeNode = (cur, catalogData) => {
  if (!cur) return;
  if (cur.code === catalogData.code) {
    cur.name = catalogData.name;
    cur.description = catalogData.description;
    return;
  }
  cur.children?.forEach((child) => editTreeNode(child, catalogData));
};

const deleteTreeNode = (cur, catalogData) => {
  if (!cur) return;
  if (cur.code === catalogData.parentCode) {
    cur.children = cur.children.filter((child) => child.code !== catalogData.code);
    return;
  }
  cur.children?.forEach((child) => deleteTreeNode(child, catalogData));
};

// 辅助方法
const initCatalogTree = (node) => {
  if (!node?.children) return;
  node.children.forEach((child) => {
    child.parentCode = node.code;
    initCatalogTree(child);
  });
};

const getNode = (key) => treeRef.value?.getNode(key);
const setCurrentKey = (key) => treeRef.value?.setCurrentKey(key);

const getCatalogName = (catalogCode) => {
  let result = "";
  const traverse = (node) => {
    if (result) return;
    if (node.code === catalogCode) {
      result = node.name;
      return;
    }
    node.children?.forEach(traverse);
  };
  traverse(moduleData.value.catalogTree);
  return result;
};

const generateCode = () => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  return Array.from({ length: 8 }, () => chars.charAt(Math.floor(Math.random() * chars.length))).join("");
};

// 生命周期
onMounted(async () => {
  events.getCatelogTree();
});

// 事件声明
const emits = defineEmits(["treeNodeClick", "treeMenuClick"]);

// 暴露接口
defineExpose({
  getNode,
  setCurrentKey,
  getModuleData: () => moduleData.value,
  editCatalog: events.editCatalog,
  getCatalogName,
});
</script>

<style lang="scss" scoped>
.left-wrap {
  overflow: auto;
  width: 300px;
  flex-shrink: 0;
  padding: 10px;

  :deep(.el-input) {
    margin: 0 0 10px 0;
  }

  .space-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    .space-label {
      margin-right: 8px;
    }
  }

  .custom-tree-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0 3px;

    .text {
      display: inline-block;
      align-content: center;
      @include no-wrap();
      word-break: break-all;
    }
  }
}

.operate-popover {
  .items {
    :deep(.el-button) {
      padding: 5px 5px 5px 0;
      display: flex;
      min-width: 80px;
      width: 100%;
      justify-content: left;

      &:hover {
        background: var(--el-color-primary-light-9);
      }
    }

    :deep(.el-button + .el-button) {
      margin-left: 0;
    }
  }
}

:deep(.el-divider--horizontal) {
  margin: 3px 0;
}
</style>
<style lang="scss">
.operate-popover {
  min-width: unset !important;
}
</style>